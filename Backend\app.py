from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import func
from dotenv import load_dotenv
import os
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS


load_dotenv()

DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
SECRET_KEY = os.getenv("SECRET_KEY")

app = Flask(__name__)

app.config['SQLALCHEMY_DATABASE_URI'] = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@localhost:{SECRET_KEY}/trial'
CORS(app)
db = SQLAlchemy(app)

limiter = Limiter(key_func=get_remote_address, app=app)

class Transaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    usd_amount = db.Column(db.Float, nullable=False)
    lbp_amount = db.Column(db.Float, nullable=False)
    usd_to_lbp = db.Column(db.<PERSON><PERSON>an, nullable=False)


@app.route('/hello', methods=['GET'])
def hello_world():
    return "Hello Worldsss"

@app.route('/transaction',methods=['POST'])
@limiter.limit("10 per minute")
def add_transaction():
    data = request.get_json()

    new_transaction = Transaction(
        usd_amount=data['usd_amount'],
        lbp_amount=data['lbp_amount'],
        usd_to_lbp=data['usd_to_lbp']
    )

    db.session.add(new_transaction)
    db.session.commit()

    return jsonify({"message":"Transaction added successfully!"})

@app.route('/exchangeRate',methods=['GET'])
def exchangerate():
    sell_totals = db.session.query(
        func.sum(Transaction.usd_amount),
        func.sum(Transaction.lbp_amount),
    ).filter_by(usd_to_lbp=True).first()
    average_usd_to_lbp = 0
    if sell_totals[0] is not None and sell_totals[1] >0:
        average_usd_to_lbp = sell_totals[0]/sell_totals[1]

    buy_totals = db.session.query(
        func.sum(Transaction.lbp_amount),
        func.sum(Transaction.usd_amount)
    ).filter_by(usd_to_lbp=False).first()

    average_lbp_to_usd = 0
    # Check if totals are not None and denominator is not zero
    if buy_totals[0] is not None and buy_totals[1] > 0:
        average_lbp_to_usd = buy_totals[0] / buy_totals[1]

    # --- Construct and return the JSON response ---
    response = {
        "usd_to_lbp": f"{average_usd_to_lbp:.2f}",
        "lbp_to_usd": f"{average_lbp_to_usd:.2f}"
    }

    return jsonify(response)

if __name__ == '__main__':
    app.run(debug=False)