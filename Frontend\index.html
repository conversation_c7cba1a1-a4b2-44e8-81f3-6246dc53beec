<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self'; connect-src http://127.0.0.1:5000;"
    />
    <title>LBP Exchange Tracker</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <!-- Header -->

    <div class="header">
      <h1>Exchange</h1>
    </div>

    <!-- Wrapper -->
    <div class="wrapper">
      <h2>Today's Exchange Rate</h2>
      <p>LBP to USD Exchange Rate</p>
      <h3>Buy USD: <span id="buy-usd-rate">Not availabe yet</span></h3>
      <h3>Sell USD: <span id="sell-usd-rate">Not availabe yet</span></h3>
      <hr />
      <h2>Record a recent transaction</h2>
      <form name="transaction-entry">
        <div class="amount-input">
          <label for="lbp-amount">LBP Amount</label>
          <input id="lbp-amount" type="'number" />
        </div>
        <div class="amount-input">
          <label for="usd-amount">USD Amount</label>
          <input id="usd-amount" type="'number" />
        </div>
        <select id="transaction-type">
          <option value="usd-to-lbp">USD to LBP</option>
          <option value="lbp-to-usd">LBP to USD</option>
        </select>
        <button id="add-button" class="button" type="button">Add</button>
      </form>
    </div>
    <script src="script.js"></script>
  </body>
</html>
