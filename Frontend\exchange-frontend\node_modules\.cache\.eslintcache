[{"C:\\Users\\<USER>\\Desktop\\EECE 430L Trial\\Frontend\\exchange-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\EECE 430L Trial\\Frontend\\exchange-frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\EECE 430L Trial\\Frontend\\exchange-frontend\\src\\App.js": "3"}, {"size": 535, "mtime": 1752339582067, "results": "4", "hashOfConfig": "5"}, {"size": 362, "mtime": 1752339582283, "results": "6", "hashOfConfig": "5"}, {"size": 90, "mtime": 1752340619413, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "v0g2th", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\EECE 430L Trial\\Frontend\\exchange-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\EECE 430L Trial\\Frontend\\exchange-frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\EECE 430L Trial\\Frontend\\exchange-frontend\\src\\App.js", [], []]