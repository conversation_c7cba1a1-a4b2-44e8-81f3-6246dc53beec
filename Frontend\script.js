// --- Configuration ---
const SERVER_URL = "http://127.0.0.1:5000";

// --- Element Selection ---
const addButton = document.getElementById("add-button");
const buyRateSpan = document.getElementById("buy-usd-rate");
const sellRateSpan = document.getElementById("sell-usd-rate");
const lbpAmountInput = document.getElementById("lbp-amount");
const usdAmountInput = document.getElementById("usd-amount");
const transactionTypeSelect = document.getElementById("transaction-type");

// --- Rate Limiting Variables ---
let transactionCount = 0;
const maxTransactionsPerMinute = 10;

// Reset transaction count every minute
setInterval(() => {
  transactionCount = 0;
}, 60000);

// --- Functions ---

/**
 * Fetches the latest exchange rates from the server and updates the UI.
 */
function fetchRates() {
  fetch(`${SERVER_URL}/exchangeRate`)
    .then((response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.json();
    })
    .then((data) => {
      sellRateSpan.innerHTML = data.usd_to_lbp;
      buyRateSpan.innerHTML = data.lbp_to_usd;
    })
    .catch((error) => {
      console.error("Error fetching exchange rates:", error);
      // Display an error message to the user if the fetch fails
      sellRateSpan.innerHTML = "Error";
      buyRateSpan.innerHTML = "Error";
    });
}

fetchRates();

/**
 * Checks if a user is allowed to make another transaction based on rate limits.
 * @returns {boolean} - True if allowed, false otherwise.
 */
function canAddTransaction() {
  if (transactionCount >= maxTransactionsPerMinute) {
    alert("Too many transactions! Please wait a minute.");
    return false;
  }
  transactionCount++;
  return true;
}

/**
 * Sends a new transaction to the server via a POST request and
 * refreshes the rates on success.
 */
function addItem() {
  if (!canAddTransaction()) {
    return;
  }

  const lbpAmount = parseFloat(lbpAmountInput.value);
  const usdAmount = parseFloat(usdAmountInput.value);

  // Validate input to ensure numbers are positive
  if (
    isNaN(lbpAmount) ||
    isNaN(usdAmount) ||
    usdAmount <= 0 ||
    lbpAmount <= 0
  ) {
    alert("Please enter valid, positive numbers for both amounts.");
    return;
  }

  // Prepare data for the POST request
  const transactionData = {
    lbp_amount: lbpAmount,
    usd_amount: usdAmount,
    usd_to_lbp: transactionTypeSelect.value === "usd-to-lbp",
  };

  // Send the new transaction to the server
  fetch(`${SERVER_URL}/transaction`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(transactionData),
  })
    .then((response) => {
      // If the response is not OK, parse the error message from the server
      if (!response.ok) {
        return response.json().then((err) => {
          throw new Error(err.error || "Server error");
        });
      }
      return response.json();
    })
    .then((data) => {
      console.log(data.message); // Log success message from server
      // Clear input fields after successful submission
      lbpAmountInput.value = "";
      usdAmountInput.value = "";
      // Fetch the latest rates to reflect the new transaction
      fetchRates();
    })
    .catch((error) => {
      console.error("Error adding transaction:", error);
      alert(`Failed to add transaction: ${error.message}`);
    });
}

// --- Initial Setup ---

// Add event listener for the "Add" button
addButton.addEventListener("click", addItem);

// Fetch the initial rates when the page has finished loading
document.addEventListener("DOMContentLoaded", fetchRates);
